const $ = id => document.getElementById(id);
const $$ = selector => document.querySelector(selector);

const state = {
  currentImage: undefined,
  originalFile: undefined,
  sizes: [],
  processedImages: [],
  isProcessing: false,
  autoProcess: true,
  compactView: false
};

const defaultSizes = [
  { id: 'tiny', name: 'Tiny', width: 100, height: 100, cropMode: 'fit', format: 'png', quality: 3, shape: 'rectangle', backgroundColor: '#ffffff', transparentBackground: false, horizontalOffset: 50, verticalOffset: 50, cornerRadius: 10, filenamePattern: '{original_name}_{width}x{height}.{format_ext}' },
  { id: 'small', name: 'Small', width: 200, height: 200, cropMode: 'fit', format: 'png', quality: 3, shape: 'rectangle', backgroundColor: '#ffffff', transparentBackground: false, horizontalOffset: 50, verticalOffset: 50, cornerRadius: 10, filenamePattern: '{original_name}_{width}x{height}.{format_ext}' },
  { id: 'medium', name: 'Medium', width: 400, height: 400, cropMode: 'fit', format: 'png', quality: 3, shape: 'rectangle', backgroundColor: '#ffffff', transparentBackground: false, horizontalOffset: 50, verticalOffset: 50, cornerRadius: 10, filenamePattern: '{original_name}_{width}x{height}.{format_ext}' },
  { id: 'large', name: 'Large', width: 800, height: 800, cropMode: 'fit', format: 'png', quality: 3, shape: 'rectangle', backgroundColor: '#ffffff', transparentBackground: false, horizontalOffset: 50, verticalOffset: 50, cornerRadius: 10, filenamePattern: '{original_name}_{width}x{height}.{format_ext}' }
];

const formatExtensions = {
  png: 'png',
  jpeg: 'jpg',
  webp: 'webp',
  gif: 'gif',
  ico: 'ico'
};

const qualityNames = {
  0: 'Fastest',
  1: 'Low',
  2: 'Medium',
  3: 'High'
};

const centeringPresets = {
  'top-left': { h: 0, v: 0 },
  'top-center': { h: 50, v: 0 },
  'top-right': { h: 100, v: 0 },
  'center-left': { h: 0, v: 50 },
  'center': { h: 50, v: 50 },
  'center-right': { h: 100, v: 50 },
  'bottom-left': { h: 0, v: 100 },
  'bottom-center': { h: 50, v: 100 },
  'bottom-right': { h: 100, v: 100 }
};

function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function loadConfig() {
  try {
    const saved = localStorage.getItem('avatar-resizer-config');
    if (saved) {
      const config = JSON.parse(saved);
      state.sizes = config.sizes || defaultSizes.map(s => ({ ...s, id: generateId() }));
      state.autoProcess = config.autoProcess ?? true;
      state.compactView = config.compactView ?? false;
    } else {
      state.sizes = defaultSizes.map(s => ({ ...s, id: generateId() }));
    }
  } catch {
    state.sizes = defaultSizes.map(s => ({ ...s, id: generateId() }));
  }
}

function saveConfig() {
  try {
    localStorage.setItem('avatar-resizer-config', JSON.stringify({
      sizes: state.sizes,
      autoProcess: state.autoProcess,
      compactView: state.compactView
    }));
  } catch {}
}

function validateFile(file) {
  const maxSize = 50 * 1024 * 1024;
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];

  if (file.size > maxSize) {
    throw new Error('File size must be less than 50MB');
  }

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Unsupported file format. Please use JPG, PNG, GIF, WEBP, or BMP');
  }
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function loadImage(file) {
  return new Promise((resolve, reject) => {
    validateFile(file);

    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

function updateUI() {
  updateSizesList();
  updateButtons();
  updateActionInfo();
  updateAutoProcessToggle();
  updateCompactView();
}

function updateButtons() {
  const hasImage = !!state.currentImage;
  const hasSizes = state.sizes.length > 0;
  const hasProcessed = state.processedImages.length > 0;

  $('processBtn').disabled = !hasImage || !hasSizes || state.isProcessing;
  $('downloadBtn').disabled = !hasProcessed || state.isProcessing;
}

function updateActionInfo() {
  const info = $('actionInfo');
  if (!state.currentImage) {
    info.textContent = 'Select an image to get started';
  } else if (state.sizes.length === 0) {
    info.textContent = 'Add at least one size configuration';
  } else if (state.isProcessing) {
    info.textContent = 'Processing images...';
  } else if (state.processedImages.length > 0) {
    info.textContent = `${state.processedImages.length} images ready for download`;
  } else {
    info.textContent = `Ready to process ${state.sizes.length} size${state.sizes.length === 1 ? '' : 's'}`;
  }
}

function updateAutoProcessToggle() {
  $('autoProcessToggle').checked = state.autoProcess;
}

function updateCompactView() {
  const list = $('sizesList');
  if (state.compactView) {
    list.classList.add('compact');
  } else {
    list.classList.remove('compact');
  }
}

function showOriginalImage() {
  const section = $('originalImageSection');
  const img = $('originalImage');
  const info = $('originalImageInfo');
  const uploadArea = $('uploadArea');

  img.src = state.currentImage.src;
  info.textContent = `${state.currentImage.naturalWidth} × ${state.currentImage.naturalHeight} • ${formatFileSize(state.originalFile.size)}`;

  section.style.display = 'block';
  uploadArea.style.display = 'none';
}

function hideOriginalImage() {
  const section = $('originalImageSection');
  const uploadArea = $('uploadArea');

  section.style.display = 'none';
  uploadArea.style.display = 'block';
}

function clearProcessedImages() {
  state.processedImages = [];
  $('processedImagesSection').style.display = 'none';
  $('successMessage').style.display = 'none';
  updateButtons();
}

async function handleImageUpload(file) {
  try {
    clearProcessedImages();

    state.originalFile = file;
    state.currentImage = await loadImage(file);

    showOriginalImage();
    updateUI();

    if (state.autoProcess && state.sizes.length > 0) {
      await processImages();
    }
  } catch (error) {
    alert(error.message);
  }
}

function setupDragAndDrop() {
  let dragCounter = 0;

  const handleDragEnter = e => {
    e.preventDefault();
    dragCounter++;
    if (dragCounter === 1) {
      document.body.classList.add('global-drag-over');
    }
  };

  const handleDragLeave = e => {
    e.preventDefault();
    dragCounter--;
    if (dragCounter === 0) {
      document.body.classList.remove('global-drag-over');
    }
  };

  const handleDragOver = e => {
    e.preventDefault();
  };

  const handleDrop = e => {
    e.preventDefault();
    dragCounter = 0;
    document.body.classList.remove('global-drag-over');

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(f => f.type.startsWith('image/'));

    if (imageFile) {
      handleImageUpload(imageFile);
    }
  };

  document.addEventListener('dragenter', handleDragEnter);
  document.addEventListener('dragleave', handleDragLeave);
  document.addEventListener('dragover', handleDragOver);
  document.addEventListener('drop', handleDrop);

  const uploadArea = $('uploadArea');
  uploadArea.addEventListener('click', () => $('fileInput').click());
}

function setupFileInput() {
  const fileInput = $('fileInput');
  fileInput.addEventListener('change', e => {
    const file = e.target.files[0];
    if (file) {
      handleImageUpload(file);
    }
  });

  $('browseBtn').addEventListener('click', e => {
    e.stopPropagation();
    fileInput.click();
  });

  $('changeImageBtn').addEventListener('click', () => {
    fileInput.click();
  });
}

function getContrastColor(hexColor) {
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#ffffff';
}

function createSizeTag(text, backgroundColor) {
  if (backgroundColor) {
    const textColor = getContrastColor(backgroundColor);
    return `<span class="size-tag" style="background-color: ${backgroundColor}; color: ${textColor}; border-color: ${backgroundColor};">${text}</span>`;
  }
  return `<span class="size-tag">${text}</span>`;
}

function renderSizeItem(size) {
  const formatText = size.format.toUpperCase();
  const qualityText = size.format === 'jpeg' || size.format === 'webp' ? `${size.jpegQuality || size.webpQuality || 85}%` :
                     size.format === 'png' ? `L${size.pngCompression ?? 6}` :
                     size.format === 'gif' ? `${size.gifColors || 256}c` :
                     qualityNames[size.quality] || 'High';

  const cropText = size.cropMode.charAt(0).toUpperCase() + size.cropMode.slice(1);
  const shapeText = size.shape === 'rectangle' ? 'Rect' :
                   size.shape === 'circle' ? 'Circle' :
                   size.shape === 'rounded' ? `R${size.cornerRadius || 10}` : 'Rect';

  const centerText = size.horizontalOffset === 50 && size.verticalOffset === 50 ? 'Center' :
                    size.horizontalOffset === 0 && size.verticalOffset === 0 ? 'TL' :
                    size.horizontalOffset === 100 && size.verticalOffset === 0 ? 'TR' :
                    size.horizontalOffset === 0 && size.verticalOffset === 100 ? 'BL' :
                    size.horizontalOffset === 100 && size.verticalOffset === 100 ? 'BR' :
                    size.horizontalOffset === 50 && size.verticalOffset === 0 ? 'TC' :
                    size.horizontalOffset === 50 && size.verticalOffset === 100 ? 'BC' :
                    size.horizontalOffset === 0 && size.verticalOffset === 50 ? 'CL' :
                    size.horizontalOffset === 100 && size.verticalOffset === 50 ? 'CR' :
                    `${size.horizontalOffset}%,${size.verticalOffset}%`;

  const bgTag = size.transparentBackground ?
    createSizeTag('Transparent') :
    createSizeTag('BG', size.backgroundColor);

  return `
    <div class="size-item" draggable="true" data-id="${size.id}">
      <div class="size-header">
        <div class="size-info">
          <div class="size-name">${size.name}</div>
          <div class="size-dimensions">${size.width} × ${size.height}</div>
        </div>
        <div class="size-actions">
          <button type="button" class="size-action-btn edit-btn" data-action="edit" data-id="${size.id}" title="Edit">✏️</button>
          <button type="button" class="size-action-btn duplicate-btn" data-action="duplicate" data-id="${size.id}" title="Duplicate">📋</button>
          <button type="button" class="size-action-btn delete-btn" data-action="delete" data-id="${size.id}" title="Delete">🗑️</button>
        </div>
      </div>
      <div class="size-tags">
        ${createSizeTag(formatText)}
        ${createSizeTag(cropText)}
        ${createSizeTag(shapeText)}
        ${createSizeTag(qualityText)}
        ${createSizeTag(centerText)}
        ${bgTag}
      </div>
    </div>
  `;
}

function updateSizesList() {
  const list = $('sizesList');
  list.innerHTML = state.sizes.map(renderSizeItem).join('');
}

function setupSizesList() {
  const list = $('sizesList');

  list.addEventListener('click', e => {
    const action = e.target.dataset.action;
    const id = e.target.dataset.id;

    if (action && id) {
      e.stopPropagation();

      if (action === 'edit') {
        editSize(id);
      } else if (action === 'duplicate') {
        duplicateSize(id);
      } else if (action === 'delete') {
        deleteSize(id);
      }
    }
  });

  let draggedElement = undefined;

  list.addEventListener('dragstart', e => {
    if (e.target.classList.contains('size-item')) {
      draggedElement = e.target;
      e.target.classList.add('dragging');
      list.classList.add('drag-active');
    }
  });

  list.addEventListener('dragend', e => {
    if (e.target.classList.contains('size-item')) {
      e.target.classList.remove('dragging');
      list.classList.remove('drag-active');
      draggedElement = undefined;
    }
  });

  list.addEventListener('dragover', e => {
    e.preventDefault();
    const afterElement = getDragAfterElement(list, e.clientY);
    if (draggedElement) {
      if (afterElement == undefined) {
        list.appendChild(draggedElement);
      } else {
        list.insertBefore(draggedElement, afterElement);
      }
    }
  });

  list.addEventListener('drop', e => {
    e.preventDefault();
    reorderSizes();
  });
}

function getDragAfterElement(container, y) {
  const draggableElements = [...container.querySelectorAll('.size-item:not(.dragging)')];

  return draggableElements.reduce((closest, child) => {
    const box = child.getBoundingClientRect();
    const offset = y - box.top - box.height / 2;

    if (offset < 0 && offset > closest.offset) {
      return { offset: offset, element: child };
    } else {
      return closest;
    }
  }, { offset: Number.NEGATIVE_INFINITY }).element;
}

function reorderSizes() {
  const items = [...$('sizesList').querySelectorAll('.size-item')];
  const newOrder = items.map(item => item.dataset.id);

  state.sizes = newOrder.map(id => state.sizes.find(s => s.id === id));
  saveConfig();
}

function addSize() {
  const newSize = {
    id: generateId(),
    name: 'New Size',
    width: 400,
    height: 400,
    cropMode: 'fit',
    format: 'png',
    quality: 3,
    shape: 'rectangle',
    backgroundColor: '#ffffff',
    transparentBackground: false,
    horizontalOffset: 50,
    verticalOffset: 50,
    cornerRadius: 10,
    filenamePattern: '{original_name}_{width}x{height}.{format_ext}',
    jpegQuality: 85,
    webpQuality: 80,
    pngCompression: 6,
    gifColors: 256
  };

  state.sizes.push(newSize);
  updateUI();
  saveConfig();
  editSize(newSize.id);
}

function editSize(id) {
  const size = state.sizes.find(s => s.id === id);
  if (!size) return;

  openModal(size);
}

function duplicateSize(id) {
  const size = state.sizes.find(s => s.id === id);
  if (!size) return;

  const duplicate = {
    ...size,
    id: generateId(),
    name: `${size.name} Copy`
  };

  state.sizes.push(duplicate);
  updateUI();
  saveConfig();
}

function deleteSize(id) {
  if (confirm('Are you sure you want to delete this size configuration?')) {
    state.sizes = state.sizes.filter(s => s.id !== id);
    updateUI();
    saveConfig();
  }
}

let currentEditingSize = undefined;

function openModal(size) {
  currentEditingSize = { ...size };

  $('modalTitle').textContent = size.id ? 'Edit Size' : 'Add Size';
  $('sizeNameInput').value = size.name || '';
  $('widthInput').value = size.width || 400;
  $('heightInput').value = size.height || 400;
  $('cropModeSelect').value = size.cropMode || 'fit';
  $('centeringPresetSelect').value = getCenteringPreset(size.horizontalOffset ?? 50, size.verticalOffset ?? 50);
  $('horizontalOffsetInput').value = size.horizontalOffset ?? 50;
  $('verticalOffsetInput').value = size.verticalOffset ?? 50;
  $('qualitySelect').value = size.quality ?? 3;
  $('formatSelect').value = size.format || 'png';
  $('shapeSelect').value = size.shape || 'rectangle';
  $('cornerRadiusInput').value = size.cornerRadius ?? 10;
  $('backgroundColorInput').value = size.backgroundColor || '#ffffff';
  $('transparentBackgroundToggle').checked = size.transparentBackground ?? false;
  $('filenamePatternInput').value = size.filenamePattern || '{original_name}_{width}x{height}.{format_ext}';

  $('jpegQualityInput').value = size.jpegQuality ?? 85;
  $('webpQualityInput').value = size.webpQuality ?? 80;
  $('pngCompressionInput').value = size.pngCompression ?? 6;
  $('gifColorsInput').value = size.gifColors ?? 256;

  updateModalUI();
  $('editSizeModal').style.display = 'flex';
}

function closeModal() {
  $('editSizeModal').style.display = 'none';
  currentEditingSize = undefined;
}

function getCenteringPreset(h, v) {
  for (const [preset, coords] of Object.entries(centeringPresets)) {
    if (coords.h === h && coords.v === v) {
      return preset;
    }
  }
  return 'custom';
}

function updateModalUI() {
  const format = $('formatSelect').value;
  const shape = $('shapeSelect').value;
  const centering = $('centeringPresetSelect').value;

  $$('.format-setting').forEach(el => el.style.display = 'none');
  $(format + '-setting')?.style.display = 'block';

  $('cornerRadiusGroup').style.display = shape === 'rounded' ? 'block' : 'none';
  $('customCenteringRow').style.display = centering === 'custom' ? 'flex' : 'none';

  const supportsTransparency = ['png', 'webp', 'gif'].includes(format);
  $$('.transparency-setting').forEach(el => el.style.display = supportsTransparency ? 'block' : 'none');

  updateSliderValues();
}

function updateSliderValues() {
  $('horizontalOffsetValue').textContent = $('horizontalOffsetInput').value + '%';
  $('verticalOffsetValue').textContent = $('verticalOffsetInput').value + '%';
  $('jpegQualityValue').textContent = $('jpegQualityInput').value + '%';
  $('webpQualityValue').textContent = $('webpQualityInput').value + '%';
  $('pngCompressionValue').textContent = $('pngCompressionInput').value;
  $('gifColorsValue').textContent = $('gifColorsInput').value;
}

function setupModal() {
  $('modalCloseBtn').addEventListener('click', closeModal);
  $('modalCancelBtn').addEventListener('click', closeModal);

  $('modalSaveBtn').addEventListener('click', () => {
    const name = $('sizeNameInput').value.trim() || 'Unnamed';
    const width = parseInt($('widthInput').value) || 400;
    const height = parseInt($('heightInput').value) || 400;

    if (width < 1 || height < 1) {
      alert('Width and height must be at least 1 pixel');
      return;
    }

    const updatedSize = {
      ...currentEditingSize,
      name,
      width,
      height,
      cropMode: $('cropModeSelect').value,
      horizontalOffset: parseInt($('horizontalOffsetInput').value),
      verticalOffset: parseInt($('verticalOffsetInput').value),
      quality: parseInt($('qualitySelect').value),
      format: $('formatSelect').value,
      shape: $('shapeSelect').value,
      cornerRadius: parseInt($('cornerRadiusInput').value),
      backgroundColor: $('backgroundColorInput').value,
      transparentBackground: $('transparentBackgroundToggle').checked,
      filenamePattern: $('filenamePatternInput').value.trim() || '{original_name}_{width}x{height}.{format_ext}',
      jpegQuality: parseInt($('jpegQualityInput').value),
      webpQuality: parseInt($('webpQualityInput').value),
      pngCompression: parseInt($('pngCompressionInput').value),
      gifColors: parseInt($('gifColorsInput').value)
    };

    if (currentEditingSize.id) {
      const index = state.sizes.findIndex(s => s.id === currentEditingSize.id);
      if (index !== -1) {
        state.sizes[index] = updatedSize;
      }
    } else {
      updatedSize.id = generateId();
      state.sizes.push(updatedSize);
    }

    updateUI();
    saveConfig();
    closeModal();
  });

  $('formatSelect').addEventListener('change', updateModalUI);
  $('shapeSelect').addEventListener('change', updateModalUI);
  $('centeringPresetSelect').addEventListener('change', e => {
    const preset = e.target.value;
    if (preset !== 'custom' && centeringPresets[preset]) {
      $('horizontalOffsetInput').value = centeringPresets[preset].h;
      $('verticalOffsetInput').value = centeringPresets[preset].v;
    }
    updateModalUI();
  });

  ['horizontalOffsetInput', 'verticalOffsetInput', 'jpegQualityInput', 'webpQualityInput', 'pngCompressionInput', 'gifColorsInput'].forEach(id => {
    $(id).addEventListener('input', updateSliderValues);
  });

  $('editSizeModal').addEventListener('click', e => {
    if (e.target === $('editSizeModal')) {
      closeModal();
    }
  });
}

function createCanvas(width, height) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
}

function calculateDimensions(sourceWidth, sourceHeight, targetWidth, targetHeight, cropMode) {
  if (cropMode === 'stretch') {
    return {
      drawWidth: targetWidth,
      drawHeight: targetHeight,
      sourceX: 0,
      sourceY: 0,
      sourceWidth,
      sourceHeight
    };
  }

  const sourceRatio = sourceWidth / sourceHeight;
  const targetRatio = targetWidth / targetHeight;

  if (cropMode === 'fit') {
    if (sourceRatio > targetRatio) {
      const drawWidth = targetWidth;
      const drawHeight = targetWidth / sourceRatio;
      return {
        drawWidth,
        drawHeight,
        sourceX: 0,
        sourceY: 0,
        sourceWidth,
        sourceHeight
      };
    } else {
      const drawWidth = targetHeight * sourceRatio;
      const drawHeight = targetHeight;
      return {
        drawWidth,
        drawHeight,
        sourceX: 0,
        sourceY: 0,
        sourceWidth,
        sourceHeight
      };
    }
  }

  if (cropMode === 'fill') {
    if (sourceRatio > targetRatio) {
      const cropWidth = sourceHeight * targetRatio;
      const cropX = (sourceWidth - cropWidth) / 2;
      return {
        drawWidth: targetWidth,
        drawHeight: targetHeight,
        sourceX: cropX,
        sourceY: 0,
        sourceWidth: cropWidth,
        sourceHeight
      };
    } else {
      const cropHeight = sourceWidth / targetRatio;
      const cropY = (sourceHeight - cropHeight) / 2;
      return {
        drawWidth: targetWidth,
        drawHeight: targetHeight,
        sourceX: 0,
        sourceY: cropY,
        sourceWidth,
        sourceHeight: cropHeight
      };
    }
  }

  return {
    drawWidth: targetWidth,
    drawHeight: targetHeight,
    sourceX: 0,
    sourceY: 0,
    sourceWidth,
    sourceHeight
  };
}

function applyShape(canvas, shape, cornerRadius, backgroundColor, transparentBackground) {
  const width = canvas.width;
  const height = canvas.height;

  if (shape === 'rectangle') return canvas;

  const tempCanvas = createCanvas(width, height);
  const tempCtx = tempCanvas.getContext('2d');

  if (!transparentBackground) {
    tempCtx.fillStyle = backgroundColor;
    tempCtx.fillRect(0, 0, width, height);
  }

  tempCtx.save();

  if (shape === 'circle') {
    const radius = Math.min(width, height) / 2;
    const centerX = width / 2;
    const centerY = height / 2;

    tempCtx.beginPath();
    tempCtx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    tempCtx.clip();
  } else if (shape === 'rounded') {
    const radius = Math.min(cornerRadius, width / 2, height / 2);

    tempCtx.beginPath();
    tempCtx.moveTo(radius, 0);
    tempCtx.lineTo(width - radius, 0);
    tempCtx.quadraticCurveTo(width, 0, width, radius);
    tempCtx.lineTo(width, height - radius);
    tempCtx.quadraticCurveTo(width, height, width - radius, height);
    tempCtx.lineTo(radius, height);
    tempCtx.quadraticCurveTo(0, height, 0, height - radius);
    tempCtx.lineTo(0, radius);
    tempCtx.quadraticCurveTo(0, 0, radius, 0);
    tempCtx.clip();
  }

  tempCtx.drawImage(canvas, 0, 0);
  tempCtx.restore();

  return tempCanvas;
}

function formatFilename(pattern, data) {
  return pattern.replace(/\{([^}]+)\}/g, (match, key) => {
    const [field, modifier] = key.split(':');
    let value = data[field] ?? match;

    if (modifier && typeof value === 'number') {
      const padding = parseInt(modifier);
      if (!isNaN(padding)) {
        value = value.toString().padStart(padding, '0');
      }
    } else if (modifier && typeof value === 'string') {
      if (modifier === 'upper') value = value.toUpperCase();
      else if (modifier === 'lower') value = value.toLowerCase();
    }

    return value;
  });
}

async function processImage(size) {
  const sourceCanvas = createCanvas(state.currentImage.naturalWidth, state.currentImage.naturalHeight);
  const sourceCtx = sourceCanvas.getContext('2d');
  sourceCtx.drawImage(state.currentImage, 0, 0);

  const dims = calculateDimensions(
    state.currentImage.naturalWidth,
    state.currentImage.naturalHeight,
    size.width,
    size.height,
    size.cropMode
  );

  let targetCanvas = createCanvas(size.width, size.height);
  const targetCtx = targetCanvas.getContext('2d');

  if (!size.transparentBackground || !['png', 'webp', 'gif'].includes(size.format)) {
    targetCtx.fillStyle = size.backgroundColor;
    targetCtx.fillRect(0, 0, size.width, size.height);
  }

  if (size.cropMode === 'fit') {
    const x = (size.width - dims.drawWidth) / 2;
    const y = (size.height - dims.drawHeight) / 2;

    const tempCanvas = createCanvas(dims.drawWidth, dims.drawHeight);
    await pica.resize(sourceCanvas, tempCanvas, { quality: size.quality });
    targetCtx.drawImage(tempCanvas, x, y);
  } else if (size.cropMode === 'fill') {
    const offsetX = (dims.sourceWidth * (size.horizontalOffset ?? 50)) / 100;
    const offsetY = (dims.sourceHeight * (size.verticalOffset ?? 50)) / 100;

    const cropCanvas = createCanvas(dims.sourceWidth, dims.sourceHeight);
    const cropCtx = cropCanvas.getContext('2d');
    cropCtx.drawImage(
      sourceCanvas,
      dims.sourceX + offsetX - dims.sourceWidth / 2,
      dims.sourceY + offsetY - dims.sourceHeight / 2,
      dims.sourceWidth,
      dims.sourceHeight,
      0,
      0,
      dims.sourceWidth,
      dims.sourceHeight
    );

    await pica.resize(cropCanvas, targetCanvas, { quality: size.quality });
  } else {
    await pica.resize(sourceCanvas, targetCanvas, { quality: size.quality });
  }

  if (size.shape !== 'rectangle') {
    targetCanvas = applyShape(targetCanvas, size.shape, size.cornerRadius, size.backgroundColor, size.transparentBackground);
  }

  const mimeType = size.format === 'jpeg' ? 'image/jpeg' :
                  size.format === 'png' ? 'image/png' :
                  size.format === 'webp' ? 'image/webp' :
                  size.format === 'gif' ? 'image/gif' :
                  size.format === 'ico' ? 'image/x-icon' : 'image/png';

  const quality = size.format === 'jpeg' ? (size.jpegQuality ?? 85) / 100 :
                 size.format === 'webp' ? (size.webpQuality ?? 80) / 100 : undefined;

  const blob = await pica.toBlob(targetCanvas, mimeType, quality);

  const now = new Date();
  const filenameData = {
    original_name: state.originalFile.name.replace(/\.[^/.]+$/, ''),
    original_ext: state.originalFile.name.split('.').pop().toLowerCase(),
    original_width: state.currentImage.naturalWidth,
    original_height: state.currentImage.naturalHeight,
    name: size.name,
    width: size.width,
    height: size.height,
    format: size.format.toUpperCase(),
    format_ext: formatExtensions[size.format],
    crop_mode: size.cropMode.charAt(0).toUpperCase() + size.cropMode.slice(1),
    shape: size.shape === 'rectangle' ? 'Rectangle' :
           size.shape === 'circle' ? 'Circle' : 'Rounded',
    quality_text: size.format === 'jpeg' || size.format === 'webp' ? `${size.jpegQuality || size.webpQuality || 85}%` :
                  qualityNames[size.quality] || 'High',
    date: now.toISOString().split('T')[0],
    time: now.toTimeString().split(' ')[0].replace(/:/g, ''),
    timestamp: Math.floor(now.getTime() / 1000)
  };

  const filename = formatFilename(size.filenamePattern, filenameData);

  return {
    blob,
    filename,
    size: size.name,
    dimensions: `${size.width} × ${size.height}`,
    format: size.format.toUpperCase(),
    fileSize: formatFileSize(blob.size),
    canvas: targetCanvas
  };
}

async function processImages() {
  if (!state.currentImage || state.sizes.length === 0 || state.isProcessing) return;

  state.isProcessing = true;
  state.processedImages = [];
  updateUI();

  try {
    for (const size of state.sizes) {
      const result = await processImage(size);
      state.processedImages.push(result);
    }

    showProcessedImages();
    showSuccessMessage();
  } catch (error) {
    alert('Error processing images: ' + error.message);
  } finally {
    state.isProcessing = false;
    updateUI();
  }
}

function showSuccessMessage() {
  const message = $('successMessage');
  $('processedCount').textContent = state.processedImages.length;
  message.style.display = 'block';

  setTimeout(() => {
    message.style.display = 'none';
  }, 5000);
}

function showProcessedImages() {
  const section = $('processedImagesSection');
  const grid = $('processedImagesGrid');
  const info = $('processedInfo');

  info.textContent = `${state.processedImages.length} images processed • Total size: ${formatFileSize(state.processedImages.reduce((sum, img) => sum + img.blob.size, 0))}`;

  grid.innerHTML = state.processedImages.map((img, index) => `
    <div class="processed-image-item">
      <a href="#" class="processed-image-link" data-index="${index}">
        <img src="${URL.createObjectURL(img.blob)}" alt="${img.filename}" class="processed-image" />
      </a>
      <div class="processed-image-info">
        <div class="processed-image-name">${img.filename}</div>
        <div class="processed-image-details">${img.dimensions} • ${img.format} • ${img.fileSize}</div>
      </div>
      <button type="button" class="processed-image-download" data-index="${index}">
        <span>💾</span> Download
      </button>
    </div>
  `).join('');

  section.style.display = 'block';
  setupPhotoSwipe();
}

function downloadImage(index) {
  const img = state.processedImages[index];
  if (!img) return;

  const url = URL.createObjectURL(img.blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = img.filename;
  a.click();
  URL.revokeObjectURL(url);
}

async function downloadAll() {
  if (state.processedImages.length === 0) return;

  const zip = new JSZip();

  state.processedImages.forEach(img => {
    zip.file(img.filename, img.blob);
  });

  try {
    const blob = await zip.generateAsync({ type: 'blob' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${state.originalFile.name.replace(/\.[^/.]+$/, '')}_resized.zip`;
    a.click();
    URL.revokeObjectURL(url);
  } catch (error) {
    alert('Error creating ZIP file: ' + error.message);
  }
}

function setupPhotoSwipe() {
  const lightbox = new PhotoSwipeLightbox({
    gallery: '#processedImagesGrid',
    children: '.processed-image-link',
    pswpModule: PhotoSwipe,
    dataSource: state.processedImages.map(img => ({
      src: URL.createObjectURL(img.blob),
      width: img.canvas.width,
      height: img.canvas.height,
      alt: img.filename
    }))
  });

  lightbox.init();
}

function exportConfig() {
  const config = {
    version: '1.0',
    sizes: state.sizes,
    autoProcess: state.autoProcess,
    compactView: state.compactView,
    exportDate: new Date().toISOString()
  };

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'avatar-resizer-config.json';
  a.click();
  URL.revokeObjectURL(url);
}

function importConfig(file) {
  const reader = new FileReader();
  reader.onload = e => {
    try {
      const config = JSON.parse(e.target.result);

      if (!config.sizes || !Array.isArray(config.sizes)) {
        throw new Error('Invalid configuration file');
      }

      if (confirm('This will replace your current size configurations. Continue?')) {
        state.sizes = config.sizes.map(s => ({ ...s, id: s.id || generateId() }));
        state.autoProcess = config.autoProcess ?? true;
        state.compactView = config.compactView ?? false;

        updateUI();
        saveConfig();
        alert('Configuration imported successfully!');
      }
    } catch (error) {
      alert('Error importing configuration: ' + error.message);
    }
  };
  reader.readAsText(file);
}

function setupEventHandlers() {
  $('addSizeBtn').addEventListener('click', addSize);
  $('processBtn').addEventListener('click', processImages);
  $('downloadBtn').addEventListener('click', downloadAll);

  $('autoProcessToggle').addEventListener('change', e => {
    state.autoProcess = e.target.checked;
    saveConfig();
  });

  $('compactViewToggle').addEventListener('click', () => {
    state.compactView = !state.compactView;
    updateUI();
    saveConfig();
  });

  $('exportConfigBtn').addEventListener('click', exportConfig);
  $('importConfigBtn').addEventListener('click', () => $('configFileInput').click());

  $('configFileInput').addEventListener('change', e => {
    const file = e.target.files[0];
    if (file) {
      importConfig(file);
    }
  });

  $('processedImagesGrid').addEventListener('click', e => {
    if (e.target.closest('.processed-image-download')) {
      const index = parseInt(e.target.closest('.processed-image-download').dataset.index);
      downloadImage(index);
    }
  });
}

function init() {
  loadConfig();
  updateUI();
  setupDragAndDrop();
  setupFileInput();
  setupSizesList();
  setupModal();
  setupEventHandlers();
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
