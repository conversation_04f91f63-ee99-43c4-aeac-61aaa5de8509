/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.5;
}

/* Header */
.header {
  text-align: center;
  padding: 2rem 0;
  background: white;
  margin-bottom: 2rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #6c757d;
  font-size: 0.95rem;
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.left-column {
  min-height: 500px;
}

.right-column {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Upload Area */
.upload-area {
  background: white;
  border: 2px dashed #e9ecef;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.upload-area:hover {
  border-color: #6c5ce7;
  background-color: #f8f9ff;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.upload-text {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.upload-subtext {
  color: #6c757d;
  margin-bottom: 1rem;
}

.browse-btn {
  background: none;
  border: none;
  color: #6c5ce7;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.browse-btn:hover {
  color: #5a4fcf;
}

.supported-formats {
  color: #6c757d;
  font-size: 0.85rem;
}

/* Original Image Section */
.original-image-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.change-image-btn {
  background: none;
  border: 1px solid #dee2e6;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  color: #6c5ce7;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.change-image-btn:hover {
  background-color: #f8f9ff;
  border-color: #6c5ce7;
}

.image-preview-container {
  text-align: center;
}

.original-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.image-info {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Success Message */
.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 2rem;
  border: 1px solid #c3e6cb;
}

.success-icon {
  margin-right: 0.5rem;
}

/* Output Sizes Section */
.output-sizes-section {
  margin-bottom: 2rem;
}

.config-buttons {
  display: flex;
  gap: 0.5rem;
}

.config-btn {
  background: none;
  border: 1px solid #dee2e6;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.config-btn:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

/* Size List */
.sizes-list {
  margin-bottom: 1rem;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.size-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
  cursor: move;
}

.size-item:hover {
  background-color: #e9ecef;
}

.size-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.size-item.drag-over {
  border: 2px dashed #007bff;
  background-color: #e3f2fd;
}

.sizes-list.drag-active {
  background-color: #f0f8ff;
  border: 2px dashed #007bff;
  border-radius: 8px;
  padding: 0.5rem;
}

.size-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.size-info {
  display: flex;
  flex-direction: column;
}

.size-name {
  font-weight: 600;
  color: #333;
}

.size-dimensions {
  color: #6c757d;
  font-size: 0.9rem;
}

.size-actions {
  display: flex;
  gap: 0.5rem;
}

.size-action-btn {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.size-action-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.edit-btn {
  color: #6c5ce7;
}

.duplicate-btn {
  color: #28a745;
}

.delete-btn {
  color: #dc3545;
}

.size-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-tag {
  background: white;
  border: 1px solid #dee2e6;
  padding: 0.15rem 0.4rem;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #6c757d;
}

.add-size-btn {
  width: 100%;
  background: #6c5ce7;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-size-btn:hover {
  background-color: #5a4fcf;
  transform: translateY(-1px);
}

.add-icon {
  font-size: 1.1rem;
}

/* Compact View Styles */
.sizes-list.compact .size-item {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}

.sizes-list.compact .size-header {
  margin-bottom: 0;
  align-items: center;
}

.sizes-list.compact .size-info {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.sizes-list.compact .size-name {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.sizes-list.compact .size-dimensions {
  font-size: 0.8rem;
  margin-left: 0;
}

.sizes-list.compact .size-tags {
  display: none;
}

.sizes-list.compact .size-actions {
  gap: 0.25rem;
}

.sizes-list.compact .size-action-btn {
  padding: 0.15rem;
  font-size: 0.8rem;
}

/* Actions Section */
.actions-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.auto-process-toggle {
  margin-bottom: 1.5rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
}

.toggle-text {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: '';
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
  background-color: #6c5ce7;
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.toggle-slider:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.process-btn {
  width: 100%;
  background: #6c5ce7;
  color: white;
  border: none;
  padding: 0.9rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.process-btn:hover:not(:disabled) {
  background-color: #5a4fcf;
  transform: translateY(-1px);
}

.process-btn:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
  transform: none;
}

.download-btn {
  width: 100%;
  background: white;
  color: #6c5ce7;
  border: 2px solid #6c5ce7;
  padding: 0.8rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.download-btn:hover:not(:disabled) {
  background-color: #6c5ce7;
  color: white;
  transform: translateY(-1px);
}

.download-btn:disabled {
  border-color: #adb5bd;
  color: #adb5bd;
  cursor: not-allowed;
  transform: none;
}

.action-info {
  color: #6c757d;
  font-size: 0.85rem;
  text-align: center;
}

/* Processed Images Section */
.processed-images-section {
  max-width: 1200px;
  margin: 3rem auto 0;
  padding: 0 1rem;
}

.processed-images-section .section-header {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.processed-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.processed-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.processed-image-item {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.2s ease;
}

.processed-image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.processed-image-link {
  display: block;
  text-decoration: none;
  margin-bottom: 1rem;
}

.processed-image {
  width: 100%;
  max-height: 150px;
  object-fit: contain;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.processed-image:hover {
  transform: scale(1.02);
}

.processed-image-info {
  margin-bottom: 1rem;
}

.processed-image-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  max-height: 2.6em; /* 2 lines * 1.3 line-height */
  word-break: break-all;
}

.processed-image-details {
  color: #6c757d;
  font-size: 0.85rem;
}

.processed-image-download {
  background: #6c5ce7;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
}

.processed-image-download:hover {
  background-color: #5a4fcf;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 1.5rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #6c5ce7;
  box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

.form-color {
  width: 100%;
  height: 2.75rem;
  padding: 0.25rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-color:focus {
  outline: none;
  border-color: #6c5ce7;
}

.form-help {
  color: #6c757d;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Format-specific settings */
.format-specific-settings {
  margin-top: 1rem;
}

.format-setting {
  margin-bottom: 1rem;
}

.quality-slider-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.25rem;
}

.quality-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.quality-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #6c5ce7;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quality-slider::-webkit-slider-thumb:hover {
  background: #5a4fcf;
  transform: scale(1.1);
}

.quality-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #6c5ce7;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.quality-slider::-moz-range-thumb:hover {
  background: #5a4fcf;
  transform: scale(1.1);
}

.quality-value {
  font-weight: 600;
  color: #6c5ce7;
  min-width: 3rem;
  text-align: center;
  font-size: 0.9rem;
}

/* Shape transparency settings */
.shape-transparency-setting {
  margin-bottom: 1rem;
}

.transparency-toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
  margin-bottom: 0.25rem;
}

.transparency-text {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #dee2e6;
}

.btn-secondary {
  background: white;
  color: #6c757d;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.btn-primary {
  background: #6c5ce7;
  color: white;
  border: 2px solid #6c5ce7;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: #5a4fcf;
  border-color: #5a4fcf;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .right-column {
    order: -1;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .processed-images-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .modal {
    margin: 1rem;
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #ccc;
  border-top-color: #6c5ce7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* PhotoSwipe Custom Styles */
.pswp__bg {
  background: rgba(0, 0, 0, 0.9);
}

.pswp__top-bar {
  background: rgba(0, 0, 0, 0.3);
}

.pswp__button {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.pswp__button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Sidebar repo link */
.repo-link {
  margin-top: 1rem;
  text-align: center;
}

.repo-link a {
  color: #6c5ce7;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}

.repo-link a:hover {
  text-decoration: underline;
  color: #5a4fcf;
}

.repo-link-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}

/* Centering and shape-specific settings */
#customCenteringRow {
  margin-top: 0.5rem;
}

#cornerRadiusGroup {
  margin-top: 0.5rem;
}

/* Global drag and drop styles */
body.global-drag-over {
  background-color: #f0f8ff;
  position: relative;
}

body.global-drag-over::before {
  content: '📁 Drop image here';
  position: fixed;
  top: 0.5em;
  left: 0.5em;
  right: 0.5em;
  bottom: 0.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  text-shadow: 0 2px 8px rgba(114, 114, 114, 0.8);
  color: #ffffff;
  border-radius: 12px;
  border: 3px dashed #d4d1e4;
  font-size: 1.5rem;
  font-weight: 600;
  z-index: 10000;
  pointer-events: none;
}

body.global-drag-over::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(108, 92, 231, 0.05);
  backdrop-filter: blur(3px);
  z-index: 9999;
  pointer-events: none;
}
